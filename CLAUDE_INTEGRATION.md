# Claude API Integration for Gemini CLI

This document describes the Claude API integration that has been added to Gemini CLI, allowing users to use Anthrop<PERSON>'s Claude models as an alternative to Google's Gemini models.

## 🚀 Features

- **Full Claude API Support**: Complete integration with Anthropic's Claude API
- **Multiple Model Support**: Support for Claude 3.5 Sonnet, Haiku, and Opus models
- **Streaming Responses**: Real-time streaming of <PERSON>'s responses
- **Tool Calling**: Function calling support compatible with Gemini's tool system
- **Seamless Switching**: Easy switching between Gemini and Claude providers
- **Format Compatibility**: Automatic conversion between Claude and Gemini API formats

## 📋 Supported Models

- **claude-sonnet-4-********** (default) - Latest Claude 4 Sonnet (most advanced)
- **claude-3-5-sonnet-********** - Most capable, balanced performance
- **claude-3-5-haiku-********** - Fastest, most cost-effective
- **claude-3-opus-********** - Most powerful for complex tasks

## 🔧 Setup

### 1. Get Claude API Key

1. Visit [Anthropic Console](https://console.anthropic.com/)
2. Create an account or sign in
3. Generate an API key

### 2. Configure Environment

Set your Claude API key as an environment variable:

```bash
export ANTHROPIC_API_KEY="sk-ant-api03-your-key-here"
```

Optionally, set a default model:

```bash
export CLAUDE_MODEL="claude-3-5-haiku-********"
```

### 3. Configure Authentication

Update your settings file (`~/.gemini/settings.json` or `.gemini/settings.json`):

```json
{
  "selectedAuthType": "claude-api-key"
}
```

Or use the interactive auth dialog when starting Gemini CLI.

## 💻 Usage

Once configured, use Gemini CLI exactly as before - it will now use Claude instead of Gemini:

```bash
# Start interactive session
gemini

# Direct prompt
gemini "Explain quantum computing"

# With files
gemini "Review this code" --files src/main.py
```

## 🔄 Switching Between Providers

You can easily switch between Claude and Gemini:

1. **Via Settings File**:
   ```json
   {
     "selectedAuthType": "claude-api-key"    // For Claude
     // or
     "selectedAuthType": "gemini-api-key"    // For Gemini
   }
   ```

2. **Via Auth Dialog**: Run `gemini` and use `/auth` command to change providers

3. **Via Environment**: Set different API keys and switch between them

## 🛠️ Technical Implementation

### Architecture

The Claude integration follows the existing Gemini CLI architecture:

- **AuthType Extension**: Added `USE_CLAUDE` to the authentication types
- **ContentGenerator Interface**: Claude implementation of the same interface used by Gemini
- **Format Adapters**: Automatic conversion between Claude and Gemini API formats
- **Streaming Support**: Full support for Claude's Server-Sent Events streaming

### Key Components

1. **ClaudeContentGenerator** (`packages/core/src/core/claudeContentGenerator.ts`)
   - Main implementation of Claude API client
   - Implements the same ContentGenerator interface as Gemini

2. **Claude Adapter** (`packages/core/src/core/claudeAdapter.ts`)
   - Converts between Gemini and Claude API formats
   - Handles message format, tool calling, and response conversion

3. **Authentication** (`packages/cli/src/config/auth.ts`)
   - Validates Claude API key configuration
   - Integrated with existing auth system

### Format Conversion

The adapter handles conversion between:

- **Messages**: Gemini's `Content[]` ↔ Claude's `messages` format
- **Tools**: Gemini's `FunctionDeclaration` ↔ Claude's `tools` format  
- **Responses**: Claude's response format ↔ Gemini's `GenerateContentResponse`
- **Streaming**: Claude's SSE events ↔ Gemini's streaming format

## ⚠️ Limitations

1. **No Embedding Support**: Claude doesn't provide embedding APIs. The `embedContent` method will throw an error.

2. **No Thinking Mode**: Claude doesn't support Gemini's "thinking" mode feature.

3. **Token Counting**: Claude doesn't have a dedicated token counting API, so token counts are estimated.

## 🧪 Testing

Run the test suite to verify the integration:

```bash
npm test packages/core/src/core/claudeContentGenerator.test.ts
```

## 🔍 Troubleshooting

### Common Issues

1. **"ANTHROPIC_API_KEY environment variable not found"**
   - Ensure you've set the `ANTHROPIC_API_KEY` environment variable
   - Check that it's available in your current shell session

2. **"Claude API error (401)"**
   - Verify your API key is correct and active
   - Check that you have sufficient credits in your Anthropic account

3. **"Claude API does not support embedding functionality"**
   - This is expected - use Gemini for embedding tasks
   - Consider a hybrid approach: Claude for text generation, Gemini for embeddings

### Debug Mode

Enable debug mode to see detailed API interactions:

```bash
DEBUG=1 gemini "your prompt"
```

## 🤝 Contributing

The Claude integration follows the same patterns as the existing Gemini integration. When contributing:

1. Maintain compatibility with the existing `ContentGenerator` interface
2. Add appropriate error handling and logging
3. Include tests for new functionality
4. Update documentation as needed

## 📚 Related Documentation

- [Configuration Guide](docs/cli/configuration.md) - Full configuration options
- [Architecture Overview](docs/architecture.md) - System architecture details
- [API Documentation](docs/core/) - Core API documentation
