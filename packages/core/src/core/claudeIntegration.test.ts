/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect } from 'vitest';
import { AuthType, createContentGeneratorConfig, createContentGenerator } from './contentGenerator.js';
import { DEFAULT_CLAUDE_MODEL, CLAUDE_SONNET_4_MODEL, CLAUDE_3_5_HAIKU_MODEL } from '../config/models.js';

describe('Claude Integration', () => {
  describe('createContentGeneratorConfig', () => {
    it('should create Claude config when USE_CLAUDE auth type is provided', async () => {
      // Mock environment variable
      const originalEnv = process.env.CLAUDE_API_KEY;
      process.env.CLAUDE_API_KEY = 'test-claude-key';

      try {
        const config = await createContentGeneratorConfig(
          'claude-3-5-sonnet-20241022',
          AuthType.USE_CLAUDE
        );

        expect(config.authType).toBe(AuthType.USE_CLAUDE);
        expect(config.apiKey).toBe('test-claude-key');
        expect(config.model).toBe('claude-3-5-sonnet-20241022');
      } finally {
        // Restore original environment
        if (originalEnv) {
          process.env.CLAUDE_API_KEY = originalEnv;
        } else {
          delete process.env.CLAUDE_API_KEY;
        }
      }
    });

    it('should use default Claude model when no model specified', async () => {
      const originalEnv = process.env.CLAUDE_API_KEY;
      process.env.CLAUDE_API_KEY = 'test-claude-key';

      try {
        const config = await createContentGeneratorConfig(
          undefined,
          AuthType.USE_CLAUDE
        );

        expect(config.model).toBe(DEFAULT_CLAUDE_MODEL);
      } finally {
        if (originalEnv) {
          process.env.CLAUDE_API_KEY = originalEnv;
        } else {
          delete process.env.CLAUDE_API_KEY;
        }
      }
    });

    it('should use CLAUDE_MODEL environment variable when available', async () => {
      const originalClaudeKey = process.env.CLAUDE_API_KEY;
      const originalClaudeModel = process.env.CLAUDE_MODEL;

      process.env.CLAUDE_API_KEY = 'test-claude-key';
      process.env.CLAUDE_MODEL = CLAUDE_3_5_HAIKU_MODEL;

      try {
        const config = await createContentGeneratorConfig(
          'claude-3-5-sonnet-20241022',
          AuthType.USE_CLAUDE
        );

        expect(config.model).toBe(CLAUDE_3_5_HAIKU_MODEL);
      } finally {
        if (originalClaudeKey) {
          process.env.CLAUDE_API_KEY = originalClaudeKey;
        } else {
          delete process.env.CLAUDE_API_KEY;
        }

        if (originalClaudeModel) {
          process.env.CLAUDE_MODEL = originalClaudeModel;
        } else {
          delete process.env.CLAUDE_MODEL;
        }
      }
    });

    it('should use Claude model when specified even with Gemini model in parameter', async () => {
      const originalEnv = process.env.CLAUDE_API_KEY;
      process.env.CLAUDE_API_KEY = 'test-claude-key';

      try {
        const config = await createContentGeneratorConfig(
          CLAUDE_SONNET_4_MODEL,
          AuthType.USE_CLAUDE
        );

        expect(config.model).toBe(CLAUDE_SONNET_4_MODEL);
      } finally {
        if (originalEnv) {
          process.env.CLAUDE_API_KEY = originalEnv;
        } else {
          delete process.env.CLAUDE_API_KEY;
        }
      }
    });

    it('should ignore Gemini model and use default Claude model when using Claude auth', async () => {
      const originalEnv = process.env.CLAUDE_API_KEY;
      process.env.CLAUDE_API_KEY = 'test-claude-key';

      try {
        const config = await createContentGeneratorConfig(
          'gemini-2.5-pro', // This should be ignored
          AuthType.USE_CLAUDE
        );

        expect(config.model).toBe(DEFAULT_CLAUDE_MODEL);
      } finally {
        if (originalEnv) {
          process.env.CLAUDE_API_KEY = originalEnv;
        } else {
          delete process.env.CLAUDE_API_KEY;
        }
      }
    });
  });

  describe('createContentGenerator', () => {
    it('should create Claude content generator for USE_CLAUDE auth type', async () => {
      const config = {
        model: 'claude-3-5-sonnet-20241022',
        apiKey: 'test-claude-key',
        authType: AuthType.USE_CLAUDE,
      };

      const generator = await createContentGenerator(config);
      
      expect(generator).toBeDefined();
      expect(typeof generator.generateContent).toBe('function');
      expect(typeof generator.generateContentStream).toBe('function');
      expect(typeof generator.countTokens).toBe('function');
      expect(typeof generator.embedContent).toBe('function');
    });

    it('should throw error for unsupported auth type', async () => {
      const config = {
        model: 'test-model',
        apiKey: 'test-key',
        authType: 'unsupported-auth' as AuthType,
      };

      await expect(createContentGenerator(config)).rejects.toThrow(
        'Error creating contentGenerator: Unsupported authType: unsupported-auth'
      );
    });
  });
});
