/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import {
  GenerateContentResponse,
  GenerateContentParameters,
  CountTokensResponse,
  CountTokensParameters,
  EmbedContentResponse,
  EmbedContentParameters,
} from '@google/genai';
import { ContentGenerator } from './contentGenerator.js';
import {
  ClaudeRequest,
  ClaudeResponse,
  ClaudeStreamEvent,
  convertGeminiToClaudeRequest,
  convertClaudeToGeminiResponse,
  convertClaudeStreamToGeminiResponse,
  estimateClaudeTokenCount,
} from './claudeAdapter.js';

export interface HttpOptions {
  headers?: Record<string, string>;
}

export class ClaudeContentGenerator implements ContentGenerator {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://api.anthropic.com/v1';
  private readonly httpOptions: HttpOptions;

  constructor(apiKey: string, httpOptions: HttpOptions = {}) {
    this.apiKey = apiKey;
    this.httpOptions = httpOptions;
  }

  private getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'x-api-key': this.apiKey,
      'anthropic-version': '2023-06-01',
      ...this.httpOptions.headers,
    };
  }

  async generateContent(request: GenerateContentParameters): Promise<GenerateContentResponse> {
    const claudeRequest = convertGeminiToClaudeRequest(request);
    
    try {
      const response = await fetch(`${this.baseUrl}/messages`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(claudeRequest),
        signal: request.config?.abortSignal,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Claude API error (${response.status}): ${errorText}`);
      }

      const claudeResponse: ClaudeResponse = await response.json();
      return convertClaudeToGeminiResponse(claudeResponse);
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw error;
      }
      throw new Error(`Failed to generate content with Claude: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async generateContentStream(request: GenerateContentParameters): Promise<AsyncGenerator<GenerateContentResponse>> {
    return this._generateContentStream(request);
  }

  private async *_generateContentStream(request: GenerateContentParameters): AsyncGenerator<GenerateContentResponse> {
    const claudeRequest = convertGeminiToClaudeRequest(request);
    claudeRequest.stream = true;

    try {
      const response = await fetch(`${this.baseUrl}/messages`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(claudeRequest),
        signal: request.config?.abortSignal,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Claude API error (${response.status}): ${errorText}`);
      }

      if (!response.body) {
        throw new Error('No response body received from Claude API');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim() === '') continue;
            
            if (line.startsWith('data: ')) {
              const dataStr = line.slice(6);
              if (dataStr === '[DONE]') break;

              try {
                const event: ClaudeStreamEvent = JSON.parse(dataStr);
                const geminiResponse = convertClaudeStreamToGeminiResponse(event);
                if (geminiResponse) {
                  yield geminiResponse;
                }
              } catch (parseError) {
                console.warn('Failed to parse Claude stream event:', parseError);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw error;
      }
      throw new Error(`Failed to stream content with Claude: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async countTokens(request: CountTokensParameters): Promise<CountTokensResponse> {
    // Claude doesn't have a dedicated token counting API
    // We'll estimate based on the request content
    const claudeRequest = convertGeminiToClaudeRequest({
      model: request.model || 'claude-3-5-sonnet-20241022',
      contents: request.contents,
      config: {},
    });

    return estimateClaudeTokenCount(claudeRequest);
  }

  async embedContent(_request: EmbedContentParameters): Promise<EmbedContentResponse> {
    // Claude doesn't provide embedding functionality
    // This could be extended to use other embedding services
    throw new Error('Claude API does not support embedding functionality. Consider using a different provider for embeddings.');
  }
}
