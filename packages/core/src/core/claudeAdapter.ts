/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import {
  Content,
  GenerateContentResponse,
  GenerateContentParameters,
  CountTokensResponse,
  CountTokensParameters,
  Part,
  GenerateContentConfig,
  FunctionDeclaration,
  FunctionCall,
  FunctionResponse,
  FinishReason,
  ContentListUnion,
  ContentUnion,
} from '@google/genai';

// Claude API types
export interface ClaudeMessage {
  role: 'user' | 'assistant';
  content: ClaudeContent[];
}

export interface ClaudeContent {
  type: 'text' | 'image' | 'tool_use' | 'tool_result';
  text?: string;
  source?: {
    type: 'base64';
    media_type: string;
    data: string;
  };
  id?: string;
  name?: string;
  input?: Record<string, unknown>;
  tool_use_id?: string;
  content?: string | ClaudeContent[];
  is_error?: boolean;
}

export interface ClaudeRequest {
  model: string;
  max_tokens: number;
  messages: ClaudeMessage[];
  system?: string;
  tools?: ClaudeTool[];
  temperature?: number;
  top_p?: number;
  stream?: boolean;
}

export interface ClaudeTool {
  name: string;
  description: string;
  input_schema: {
    type: 'object';
    properties: Record<string, unknown>;
    required?: string[];
  };
}

export interface ClaudeResponse {
  id: string;
  type: 'message';
  role: 'assistant';
  content: ClaudeContent[];
  model: string;
  stop_reason: 'end_turn' | 'max_tokens' | 'stop_sequence' | 'tool_use';
  stop_sequence?: string;
  usage: {
    input_tokens: number;
    output_tokens: number;
  };
}

export interface ClaudeStreamEvent {
  type: 'message_start' | 'content_block_start' | 'content_block_delta' | 'content_block_stop' | 'message_delta' | 'message_stop';
  message?: Partial<ClaudeResponse>;
  content_block?: ClaudeContent;
  delta?: {
    type: 'text_delta' | 'input_json_delta';
    text?: string;
    partial_json?: string;
  };
  index?: number;
  usage?: {
    output_tokens: number;
  };
}

/**
 * Converts Gemini Content array to Claude messages format
 */
export function convertGeminiToClaudeMessages(contents: Content[]): { messages: ClaudeMessage[]; system?: string } {
  const messages: ClaudeMessage[] = [];
  let system: string | undefined;

  for (const content of contents) {
    if (content.role === 'system') {
      // Extract system instruction
      const systemText = content.parts?.find(p => p.text)?.text;
      if (systemText) {
        system = systemText;
      }
      continue;
    }

    const claudeContent: ClaudeContent[] = [];
    
    if (content.parts) {
      for (const part of content.parts) {
        if (part.text) {
          claudeContent.push({
            type: 'text',
            text: part.text,
          });
        } else if (part.inlineData) {
          claudeContent.push({
            type: 'image',
            source: {
              type: 'base64',
              media_type: part.inlineData.mimeType || 'image/jpeg',
              data: part.inlineData.data || '',
            },
          });
        } else if (part.functionCall) {
          claudeContent.push({
            type: 'tool_use',
            id: part.functionCall.name + '_' + Date.now(),
            name: part.functionCall.name,
            input: part.functionCall.args || {},
          });
        } else if (part.functionResponse) {
          claudeContent.push({
            type: 'tool_result',
            tool_use_id: part.functionResponse.name + '_' + Date.now(),
            content: JSON.stringify(part.functionResponse.response),
          });
        }
      }
    }

    if (claudeContent.length > 0) {
      messages.push({
        role: content.role === 'model' ? 'assistant' : 'user',
        content: claudeContent,
      });
    }
  }

  return { messages, system };
}

/**
 * Converts Gemini FunctionDeclarations to Claude tools format
 */
export function convertGeminiToolsToClaudeTools(tools?: FunctionDeclaration[]): ClaudeTool[] | undefined {
  if (!tools || tools.length === 0) return undefined;

  return tools.map(tool => ({
    name: tool.name || 'unknown_function',
    description: tool.description || '',
    input_schema: {
      type: 'object',
      properties: tool.parameters?.properties || {},
      required: tool.parameters?.required || [],
    },
  }));
}

/**
 * Converts Claude response to Gemini GenerateContentResponse format
 */
export function convertClaudeToGeminiResponse(claudeResponse: ClaudeResponse): GenerateContentResponse {
  const parts: Part[] = [];

  for (const content of claudeResponse.content) {
    if (content.type === 'text' && content.text) {
      parts.push({ text: content.text });
    } else if (content.type === 'tool_use') {
      parts.push({
        functionCall: {
          name: content.name!,
          args: content.input || {},
        } as FunctionCall,
      });
    }
  }

  const finishReason = mapClaudeStopReasonToGemini(claudeResponse.stop_reason);

  const response = new GenerateContentResponse();
  response.candidates = [{
    content: {
      role: 'model',
      parts,
    },
    finishReason,
  }];
  response.usageMetadata = {
    promptTokenCount: claudeResponse.usage.input_tokens,
    candidatesTokenCount: claudeResponse.usage.output_tokens,
    totalTokenCount: claudeResponse.usage.input_tokens + claudeResponse.usage.output_tokens,
  };
  return response;
}

/**
 * Converts Claude stream event to Gemini GenerateContentResponse format
 */
export function convertClaudeStreamToGeminiResponse(event: ClaudeStreamEvent): GenerateContentResponse | null {
  if (event.type === 'content_block_delta' && event.delta?.text) {
    const response = new GenerateContentResponse();
    response.candidates = [{
      content: {
        role: 'model',
        parts: [{ text: event.delta.text }],
      },
      finishReason: FinishReason.STOP,
    }];
    return response;
  }

  if (event.type === 'content_block_start' && event.content_block?.type === 'tool_use') {
    const toolUse = event.content_block;
    const response = new GenerateContentResponse();
    response.candidates = [{
      content: {
        role: 'model',
        parts: [{
          functionCall: {
            name: toolUse.name!,
            args: toolUse.input || {},
          } as FunctionCall,
        }],
      },
      finishReason: FinishReason.STOP,
    }];
    return response;
  }

  if (event.type === 'content_block_delta' && event.delta?.partial_json) {
    // Handle partial JSON for tool arguments
    const response = new GenerateContentResponse();
    response.candidates = [{
      content: {
        role: 'model',
        parts: [{ text: event.delta.partial_json }],
      },
      finishReason: FinishReason.STOP,
    }];
    return response;
  }

  if (event.type === 'message_stop' && event.usage) {
    const response = new GenerateContentResponse();
    response.candidates = [{
      content: {
        role: 'model',
        parts: [],
      },
      finishReason: FinishReason.STOP,
    }];
    response.usageMetadata = {
      promptTokenCount: 0,
      candidatesTokenCount: event.usage.output_tokens,
      totalTokenCount: event.usage.output_tokens,
    };
    return response;
  }

  return null;
}

/**
 * Maps Claude stop reason to Gemini finish reason
 */
function mapClaudeStopReasonToGemini(stopReason: string): FinishReason {
  switch (stopReason) {
    case 'end_turn':
      return FinishReason.STOP;
    case 'max_tokens':
      return FinishReason.MAX_TOKENS;
    case 'stop_sequence':
      return FinishReason.STOP;
    case 'tool_use':
      return FinishReason.STOP;
    default:
      return FinishReason.OTHER;
  }
}

/**
 * Helper function to convert ContentListUnion to Content[]
 */
function convertContentListUnionToContents(contents: ContentListUnion): Content[] {
  if (Array.isArray(contents)) {
    // it's a Content[] or a PartsUnion[]
    return contents.map(convertContentUnionToContent);
  }
  // it's a Content or a PartsUnion
  return [convertContentUnionToContent(contents)];
}

/**
 * Helper function to convert ContentUnion to Content
 */
function convertContentUnionToContent(content: ContentUnion): Content {
  if (Array.isArray(content)) {
    // it's a PartsUnion[]
    return {
      role: 'user',
      parts: content.map(part => typeof part === 'string' ? { text: part } : part),
    };
  }
  if (typeof content === 'string') {
    // it's a string
    return {
      role: 'user',
      parts: [{ text: content }],
    };
  }
  if ('parts' in content) {
    // it's a Content
    return content;
  }
  // it's a Part
  return {
    role: 'user',
    parts: [content as Part],
  };
}

/**
 * Converts Gemini GenerateContentParameters to Claude request format
 */
export function convertGeminiToClaudeRequest(params: GenerateContentParameters): ClaudeRequest {
  // Convert ContentListUnion to Content[] using the same pattern as existing code
  const contents = convertContentListUnionToContents(params.contents);
  const { messages, system } = convertGeminiToClaudeMessages(contents);
  const tools = convertGeminiToolsToClaudeTools(params.config?.tools as FunctionDeclaration[]);

  const request: ClaudeRequest = {
    model: params.model,
    max_tokens: params.config?.maxOutputTokens || 4096,
    messages,
    temperature: params.config?.temperature,
    top_p: params.config?.topP,
  };

  if (system) {
    request.system = system;
  }

  if (tools && tools.length > 0) {
    request.tools = tools;
  }

  return request;
}

/**
 * Estimates token count for Claude request (approximate)
 */
export function estimateClaudeTokenCount(request: ClaudeRequest): CountTokensResponse {
  let totalTokens = 0;

  // Rough estimation: ~4 characters per token
  if (request.system) {
    totalTokens += Math.ceil(request.system.length / 4);
  }

  for (const message of request.messages) {
    for (const content of message.content) {
      if (content.type === 'text' && content.text) {
        totalTokens += Math.ceil(content.text.length / 4);
      }
    }
  }

  return {
    totalTokens,
  } as CountTokensResponse;
}
