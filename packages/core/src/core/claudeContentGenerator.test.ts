/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ClaudeContentGenerator } from './claudeContentGenerator.js';
import { AuthType } from './contentGenerator.js';

// Mock fetch globally
global.fetch = vi.fn();

describe('ClaudeContentGenerator', () => {
  let generator: ClaudeContentGenerator;
  const mockApiKey = 'sk-ant-api03-test-key';

  beforeEach(() => {
    generator = new ClaudeContentGenerator(mockApiKey);
    vi.clearAllMocks();
  });

  describe('generateContent', () => {
    it('should generate content successfully', async () => {
      const mockResponse = {
        id: 'msg_test',
        type: 'message',
        role: 'assistant',
        content: [{ type: 'text', text: 'Hello, world!' }],
        model: 'claude-3-5-sonnet-20241022',
        stop_reason: 'end_turn',
        usage: {
          input_tokens: 10,
          output_tokens: 5,
        },
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      });

      const result = await generator.generateContent({
        model: 'claude-3-5-sonnet-20241022',
        contents: [{ role: 'user', parts: [{ text: 'Hello' }] }],
        config: {},
      });

      expect(result.candidates).toHaveLength(1);
      expect(result.candidates[0].content.parts[0].text).toBe('Hello, world!');
      expect(result.usageMetadata?.totalTokenCount).toBe(15);
    });

    it('should handle API errors', async () => {
      (global.fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 400,
        text: () => Promise.resolve('Bad Request'),
      });

      await expect(
        generator.generateContent({
          model: 'claude-3-5-sonnet-20241022',
          contents: [{ role: 'user', parts: [{ text: 'Hello' }] }],
          config: {},
        })
      ).rejects.toThrow('Claude API error (400): Bad Request');
    });

    it('should handle abort signals', async () => {
      const controller = new AbortController();
      controller.abort();

      (global.fetch as any).mockRejectedValueOnce(new Error('AbortError'));

      await expect(
        generator.generateContent({
          model: 'claude-3-5-sonnet-20241022',
          contents: [{ role: 'user', parts: [{ text: 'Hello' }] }],
          config: { abortSignal: controller.signal },
        })
      ).rejects.toThrow();
    });
  });

  describe('countTokens', () => {
    it('should estimate token count', async () => {
      const result = await generator.countTokens({
        model: 'claude-3-5-sonnet-20241022',
        contents: [{ role: 'user', parts: [{ text: 'Hello world' }] }],
      });

      expect(result.totalTokens).toBeGreaterThan(0);
    });
  });

  describe('embedContent', () => {
    it('should throw error for unsupported embedding', async () => {
      await expect(
        generator.embedContent({
          model: 'claude-3-5-sonnet-20241022',
          content: { role: 'user', parts: [{ text: 'Hello' }] },
        })
      ).rejects.toThrow('Claude API does not support embedding functionality');
    });
  });

  describe('headers', () => {
    it('should set correct headers', () => {
      const headers = (generator as any).getHeaders();
      
      expect(headers['Content-Type']).toBe('application/json');
      expect(headers['x-api-key']).toBe(mockApiKey);
      expect(headers['anthropic-version']).toBe('2023-06-01');
    });

    it('should merge custom headers', () => {
      const customGenerator = new ClaudeContentGenerator(mockApiKey, {
        headers: { 'Custom-Header': 'test-value' },
      });
      
      const headers = (customGenerator as any).getHeaders();
      
      expect(headers['Custom-Header']).toBe('test-value');
      expect(headers['x-api-key']).toBe(mockApiKey);
    });
  });
});
